import { NextRequest, NextResponse } from 'next/server';
import * as sdk from "microsoft-cognitiveservices-speech-sdk";
import { PassThrough } from 'stream';

export async function POST(req: NextRequest) {
    const formData = await req.formData();
    const audioFile = formData.get('audio') as File;
    const referenceText = formData.get('text') as string;

    if (!audioFile || !referenceText) {
        return NextResponse.json({ error: 'Audio file and reference text are required.' }, { status: 400 });
    }

    const speechKey = process.env.SPEECH_KEY;
    const speechRegion = process.env.SPEECH_REGION;

    if (!speechKey || !speechRegion) {
        return NextResponse.json({ error: 'Speech key or region not set.' }, { status: 500 });
    }

    const speechConfig = sdk.SpeechConfig.fromSubscription(speechKey, speechRegion);
    speechConfig.speechRecognitionLanguage = "en-US";

    const audioBuffer = Buffer.from(await audioFile.arrayBuffer());

    const pushStream = sdk.AudioInputStream.createPushStream();
    pushStream.write(audioBuffer);
    pushStream.close();

    const audioConfig = sdk.AudioConfig.fromStreamInput(pushStream);
    
    const recognizer = new sdk.SpeechRecognizer(speechConfig, audioConfig);

    const pronunciationAssessmentConfig = new sdk.PronunciationAssessmentConfig(
        referenceText,
        sdk.PronunciationAssessmentGradingSystem.HundredMark,
        sdk.PronunciationAssessmentGranularity.Phoneme,
        true
    );
    pronunciationAssessmentConfig.enableProsodyAssessment = true;
    pronunciationAssessmentConfig.applyTo(recognizer);

    try {
        const result = await new Promise<sdk.SpeechRecognitionResult>((resolve, reject) => {
            recognizer.recognizeOnceAsync(
                (result: sdk.SpeechRecognitionResult) => resolve(result),
                (error: string) => reject(new Error(error))
            );
        });

        recognizer.close();

        if (result.reason === sdk.ResultReason.RecognizedSpeech) {
            const pronunciationResult = sdk.PronunciationAssessmentResult.fromResult(result);
            const resultJson = result.properties.getProperty(sdk.PropertyId.SpeechServiceResponse_JsonResult);
            
            return NextResponse.json({
                ...pronunciationResult,
                detailedResult: JSON.parse(resultJson)
            });

        } else if (result.reason === sdk.ResultReason.NoMatch) {
            return NextResponse.json({ error: 'Speech could not be recognized.' }, { status: 500 });
        } else {
            const cancellation = sdk.CancellationDetails.fromResult(result);
            return NextResponse.json({ error: `CANCELED: Reason=${cancellation.reason}` }, { status: 500 });
        }

    } catch (error) {
        let errorMessage = 'Failed to assess pronunciation.';
        if (error instanceof Error) {
          errorMessage = error.message;
        }
        console.error('ERROR:', error);
        return NextResponse.json({ error: errorMessage }, { status: 500 });
    }
}
