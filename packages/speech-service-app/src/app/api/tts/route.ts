import { NextRequest, NextResponse } from 'next/server';
import * as sdk from "microsoft-cognitiveservices-speech-sdk";

export async function POST(req: NextRequest) {
  const { text } = await req.json();

  if (!text) {
    return NextResponse.json({ error: 'Text is required.' }, { status: 400 });
  }

  const speechKey = process.env.SPEECH_KEY;
  const speechRegion = process.env.SPEECH_REGION;

  if (!speechKey || !speechRegion) {
    return NextResponse.json({ error: 'Speech key or region not set.' }, { status: 500 });
  }

  const speechConfig = sdk.SpeechConfig.fromSubscription(speechKey, speechRegion);
  speechConfig.speechSynthesisOutputFormat = sdk.SpeechSynthesisOutputFormat.Audio16Khz32KBitRateMonoMp3;
  speechConfig.speechSynthesisVoiceName = "en-US-TonyNeural";

  const synthesizer = new sdk.SpeechSynthesizer(speechConfig);

  try {
    const audioData = await new Promise<ArrayBuffer>((resolve, reject) => {
      synthesizer.speakTextAsync(
        text,
        result => {
          if (result.reason === sdk.ResultReason.SynthesizingAudioCompleted) {
            resolve(result.audioData);
          } else {
            reject(new Error(result.errorDetails));
          }
          synthesizer.close();
        },
        err => {
          reject(err);
          synthesizer.close();
        }
      );
    });

    return new NextResponse(audioData, {
      status: 200,
      headers: {
        'Content-Type': 'audio/mpeg',
      },
    });

  } catch (error) {
    let errorMessage = 'Failed to generate speech.';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    console.error('ERROR:', error);
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
