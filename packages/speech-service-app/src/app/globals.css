/* Basic Reset & App Container */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    margin: 0;
    background-color: #f0f2f5;
    color: #333;
}

.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 600px;
    margin: 0 auto;
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

/* Header */
.app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background-color: #fff;
    border-bottom: 1px solid #ddd;
}

.app-logo {
    font-size: 1.2rem;
    font-weight: bold;
}

.menu-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Side Navigation */
.side-nav {
    position: fixed;
    top: 0;
    right: -250px; /* Initially hidden */
    width: 250px;
    height: 100%;
    background-color: #fff;
    box-shadow: -2px 0 5px rgba(0,0,0,0.1);
    transition: right 0.3s ease-in-out;
    z-index: 1000;
}

.side-nav.open {
    right: 0;
}

.side-nav ul {
    list-style: none;
    padding: 0;
    margin-top: 4rem;
}

.side-nav li a {
    display: block;
    padding: 1rem 1.5rem;
    text-decoration: none;
    color: #333;
    border-bottom: 1px solid #f0f0f0;
}

.side-nav li a:hover {
    background-color: #f9f9f9;
}


/* Main Content */
.main-content {
    flex-grow: 1;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* Text Input */
.text-input-container textarea {
    width: 100%;
    height: 150px;
    padding: 0.75rem;
    font-size: 1rem;
    border: 1px solid #ccc;
    border-radius: 8px;
    resize: vertical;
    box-sizing: border-box;
}

/* Player Styles */
.player-container {
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 1rem;
    background-color: #f9f9f9;
}

.player-label {
    font-weight: bold;
    margin-bottom: 0.5rem;
}

audio {
    width: 100%;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

.action-buttons button {
    width: 100%;
    max-width: 300px;
    padding: 1rem;
    font-size: 1.1rem;
    font-weight: bold;
    border-radius: 25px;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
}

#generate-audio-btn {
    background-color: #007bff;
    color: white;
}

#record-btn {
    background-color: #dc3545;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
}

.mic-icon {
    font-size: 1.2rem;
}

button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

/* Recording Status */
#recording-status {
    text-align: center;
    font-size: 1.2rem;
    color: #dc3545;
}

#timer {
    font-weight: bold;
}

.scores-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.scores-grid div {
    background-color: #fff;
    padding: 0.75rem;
    border-radius: 6px;
    border: 1px solid #eee;
    text-align: center;
}

.scores-grid div span {
    font-weight: bold;
    font-size: 1.2rem;
    color: #007bff;
    display: block;
    margin-top: 0.25rem;
}
