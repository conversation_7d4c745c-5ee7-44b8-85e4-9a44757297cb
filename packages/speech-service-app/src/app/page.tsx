"use client";

import { useState, useRef, useEffect } from 'react';

// Define a type for the assessment result for better type checking
interface AssessmentResult {
  accuracyScore?: number;
  fluencyScore?: number;
  completenessScore?: number;
  pronScore?: number;
  prosodyScore?: number;
  detailedResult?: any;
}

const AssessmentScores: React.FC<{ result: AssessmentResult }> = ({ result }) => (
  <div className="player-container">
    <div className="player-label">📊 Assessment Scores</div>
    <div className="scores-grid">
      <div>Accuracy: <span>{result.accuracyScore?.toFixed(1)}</span></div>
      <div>Fluency: <span>{result.fluencyScore?.toFixed(1)}</span></div>
      <div>Completeness: <span>{result.completenessScore?.toFixed(1)}</span></div>
      <div>Pronunciation: <span>{result.pronScore?.toFixed(1)}</span></div>
      <div>Prosody: <span>{result.prosodyScore?.toFixed(1)}</span></div>
    </div>
  </div>
);

export default function Home() {
  const [text, setText] = useState('');
  const [ttsAudioUrl, setTtsAudioUrl] = useState('');
  const [userAudioUrl, setUserAudioUrl] = useState('');
  const [isNavOpen, setIsNavOpen] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [recordBtnDisabled, setRecordBtnDisabled] = useState(true);
  const [timer, setTimer] = useState('00:00');
  const [assessmentResult, setAssessmentResult] = useState<AssessmentResult | null>(null);

  const mediaRecorder = useRef<MediaRecorder | null>(null);
  const audioChunks = useRef<Blob[]>([]);
  const timerInterval = useRef<NodeJS.Timeout | null>(null);
  const seconds = useRef(0);

  const ttsAudioRef = useRef<HTMLAudioElement>(null);
  const userAudioRef = useRef<HTMLAudioElement>(null);

  const handleGenerateAudio = async () => {
    if (!text.trim()) {
      alert('Please enter some text to generate audio.');
      return;
    }
    try {
      const response = await fetch('/api/tts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text }),
      });
      if (!response.ok) throw new Error('Failed to generate speech.');
      const audioBlob = await response.blob();
      const audioUrl = URL.createObjectURL(audioBlob);
      setTtsAudioUrl(audioUrl);
      setRecordBtnDisabled(false);
      setAssessmentResult(null);
    } catch (error) {
      console.error('Error fetching TTS audio:', error);
      alert('Could not generate audio. Please ensure the backend server is running.');
    }
  };

  const assessPronunciation = async (audioBlob: Blob) => {
    const formData = new FormData();
    formData.append('audio', audioBlob, 'recording.wav');
    formData.append('text', text);

    try {
      const response = await fetch('/api/assessment', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) throw new Error('Failed to get assessment.');

      const result = await response.json();
      setAssessmentResult(result);
    } catch (error) {
      console.error('Error assessing pronunciation:', error);
      alert('Could not assess pronunciation. Please ensure the backend server is running.');
    }
  };

  const startRecording = async () => {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      alert('Your browser does not support the required audio APIs.');
      return;
    }
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaRecorder.current = new MediaRecorder(stream);
      mediaRecorder.current.ondataavailable = (event) => audioChunks.current.push(event.data);
      mediaRecorder.current.onstop = () => {
        const audioBlob = new Blob(audioChunks.current, { type: 'audio/wav' });
        const audioUrl = URL.createObjectURL(audioBlob);
        setUserAudioUrl(audioUrl);
        audioChunks.current = [];
        assessPronunciation(audioBlob);
      };
      mediaRecorder.current.start();
      setIsRecording(true);
    } catch (err) {
      console.error('Error accessing microphone:', err);
      alert('Could not access the microphone. Please check your permissions.');
    }
  };

  const stopRecording = () => {
    if (mediaRecorder.current && mediaRecorder.current.state === 'recording') {
      mediaRecorder.current.stop();
      setIsRecording(false);
    }
  };

  const handleRecordClick = () => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };

  const startTimer = () => {
    seconds.current = 0;
    setTimer('00:00');
    timerInterval.current = setInterval(() => {
      seconds.current++;
      const minutes = Math.floor(seconds.current / 60).toString().padStart(2, '0');
      const secs = (seconds.current % 60).toString().padStart(2, '0');
      setTimer(`${minutes}:${secs}`);
    }, 1000);
  };

  const stopTimer = () => {
    if(timerInterval.current) {
      clearInterval(timerInterval.current);
    }
  };
  
  useEffect(() => {
    if (isRecording) {
      startTimer();
    } else {
      stopTimer();
    }
  }, [isRecording]);

  return (
    <div className="app-container">
      <header className="app-header">
        <div className="app-logo">Pronunciation Practice</div>
        <button className="menu-btn" onClick={() => setIsNavOpen(!isNavOpen)}>☰</button>
      </header>
      <nav className={`side-nav ${isNavOpen ? 'open' : ''}`}>
        <ul>
          <li><a href="#">Practice</a></li>
          <li><a href="#">Settings</a></li>
          <li><a href="#">Send Feedback</a></li>
          <li><a href="#">Rate the App</a></li>
        </ul>
      </nav>
      <main className="main-content">
        <div className="text-input-container">
          <textarea id="text-input" placeholder="Type text to practice..." value={text} onChange={(e) => setText(e.target.value)}></textarea>
        </div>

        {ttsAudioUrl && (
          <div className="player-container">
            <div className="player-label">🔊 Native Audio</div>
            <div className="player-controls">
              <audio ref={ttsAudioRef} src={ttsAudioUrl} controls />
            </div>
          </div>
        )}

        {userAudioUrl && (
          <div className="player-container">
            <div className="player-label">🎙️ Your Recording</div>
            <div className="player-controls">
              <audio ref={userAudioRef} src={userAudioUrl} controls />
            </div>
          </div>
        )}

        {assessmentResult && <AssessmentScores result={assessmentResult} />}

        <div className="action-buttons">
          <button id="generate-audio-btn" onClick={handleGenerateAudio}>Generate Audio</button>
          <button id="record-btn" className="record-button" disabled={recordBtnDisabled} onClick={handleRecordClick} style={{ backgroundColor: isRecording ? '#28a745' : '#dc3545' }}>
            <span className="mic-icon">{isRecording ? '■' : '🎤'}</span> {isRecording ? 'Stop' : 'Record'}
          </button>
        </div>
        {isRecording && (
          <div id="recording-status">
            <span id="timer">{timer}</span>
          </div>
        )}
      </main>
    </div>
  );
}