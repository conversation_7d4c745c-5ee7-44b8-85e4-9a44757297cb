import "dotenv/config";
import { readFileSync } from "fs";
import {
  SpeechConfig,
  AudioConfig,
  ConversationTranscriber,
  CancellationReason,
} from "microsoft-cognitiveservices-speech-sdk";
// This example requires environment variables named "<PERSON>EECH_KEY" and "SPEECH_REGION"
const speechConfig = SpeechConfig.fromSubscription(
  process.env.SPEECH_KEY!,
  process.env.SPEECH_REGION!,
);
speechConfig.speechRecognitionLanguage = "en-US";
function fromFile() {
  // const filename = "/Users/<USER>/Documents/1.wav";
  const filename = "/Users/<USER>/Downloads/1.wav";
  const audioConfig = AudioConfig.fromWavFileInput(readFileSync(filename));
  const conversationTranscriber = new ConversationTranscriber(
    speechConfig,
    audioConfig,
  );

  console.log("Transcribing from: " + filename);
  conversationTranscriber.sessionStarted = function (_, e) {
    console.log("SessionStarted event");
    console.log("SessionId:" + e.sessionId);
  };
  conversationTranscriber.sessionStopped = function (_, e) {
    console.log("SessionStopped event");
    console.log("SessionId:" + e.sessionId);
    conversationTranscriber.stopTranscribingAsync();
  };
  conversationTranscriber.canceled = function (_, e) {
    console.log(`CANCELED: Reason=${CancellationReason[e.reason]}`);
    if (e.reason == CancellationReason.Error) {
      console.log(`CANCELED: ErrorCode=${e.errorCode}`);
      console.log(`CANCELED: ErrorDetails=${e.errorDetails}`);
      console.log(
        "CANCELED: Did you set the speech resource key and region values?",
      );
    }
    conversationTranscriber.stopTranscribingAsync();
  };
  conversationTranscriber.transcribed = function (_, e) {
    console.log(
      "TRANSCRIBED: Text=" +
        e.result.text +
        " Speaker ID=" +
        e.result.speakerId,
    );
  };
  // Start conversation transcription
  conversationTranscriber.startTranscribingAsync(
    function () {},
    function (err) {
      console.trace("err - starting transcription: " + err);
    },
  );
}
fromFile();
