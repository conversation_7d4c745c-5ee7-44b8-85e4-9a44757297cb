// Import the Speech SDK
import * as sdk from "microsoft-cognitiveservices-speech-sdk";
import fs from "fs";

// Replace with your own subscription key and service region.
const subscriptionKey: string | undefined = process.env.SPEECH_KEY;
const serviceRegion: string | undefined = process.env.SPEECH_REGION;

if (!subscriptionKey || !serviceRegion) {
  console.error(
    "Please set the SPEECH_SUBSCRIPTION_KEY and SPEECH_SERVICE_REGION environment variables.",
  );
  process.exit(1);
}

// The audio file to analyze
// const audioFile: string = "/Users/<USER>/Downloads/74ea8521-f46b-4460-8e47-2b38767d2a30.wav"
const audioFile: string = "/Users/<USER>/Downloads/2.wav"
// $”1.wav";
// const audioFile = "path/to/your/audio.wav"; // Replace with the path to your audio file

// The reference text for the pronunciation assessment.
// This is the text the user was supposed to say.
const referenceText: string = "My Name is Apple";

function createSpeechRecognizer(
  key: string,
  region: string,
  audioFileName: string,
): sdk.SpeechRecognizer {
  const audioConfig = sdk.AudioConfig.fromWavFileInput(
    fs.readFileSync(audioFileName),
  );
  const speechConfig = sdk.SpeechConfig.fromSubscription(key, region);
  // Set the language of the speech to be recognized.
  speechConfig.speechRecognitionLanguage = "en-US";
  return new sdk.SpeechRecognizer(speechConfig, audioConfig);
}

function performPronunciationAssessment(): void {
  const speechRecognizer: sdk.SpeechRecognizer = createSpeechRecognizer(
    subscriptionKey,
    serviceRegion,
    audioFile,
  );

  // Create the pronunciation assessment configuration.
  const pronunciationAssessmentConfig = new sdk.PronunciationAssessmentConfig(
    referenceText,
    sdk.PronunciationAssessmentGradingSystem.HundredMark,
    sdk.PronunciationAssessmentGranularity.Phoneme,
    true, // Enable miscue calculation
  );

  // Enable prosody assessment for more detailed feedback on rhythm and intonation.
  pronunciationAssessmentConfig.enableProsodyAssessment = true;

  // Apply the configuration to the speech recognizer.
  pronunciationAssessmentConfig.applyTo(speechRecognizer);

  console.log("Starting pronunciation assessment...");

  speechRecognizer.recognizeOnceAsync(
    (result: sdk.SpeechRecognitionResult) => {
      if (result.reason === sdk.ResultReason.RecognizedSpeech) {
        console.log(`RECOGNIZED TEXT: ${result.text}`);

        // Get the pronunciation assessment result from the speech recognition result.
        const pronunciationResult: sdk.PronunciationAssessmentResult =
          sdk.PronunciationAssessmentResult.fromResult(result);

        console.log("\n--- Pronunciation Assessment Scores ---");
        console.log(`Accuracy Score: ${pronunciationResult.accuracyScore}`);
        console.log(`Fluency Score: ${pronunciationResult.fluencyScore}`);
        console.log(
          `Completeness Score: ${pronunciationResult.completenessScore}`,
        );
        console.log(`Pronunciation Score: ${pronunciationResult.pronScore}`);
        console.log(`Prosody Score: ${pronunciationResult.prosodyScore}`);

        // The full result is also available as a JSON string
        const resultJson: string = result.properties.getProperty(
          sdk.PropertyId.SpeechServiceResponse_JsonResult,
        );
        console.log("\n--- Detailed JSON Result ---");
        console.log(JSON.stringify(JSON.parse(resultJson), null, 4));
      } else if (result.reason === sdk.ResultReason.NoMatch) {
        console.log("NOMATCH: Speech could not be recognized.");
      } else if (result.reason === sdk.ResultReason.Canceled) {
        const cancellation: sdk.CancellationDetails =
          sdk.CancellationDetails.fromResult(result);
        console.log(`CANCELED: Reason=${cancellation.reason}`);

        if (cancellation.reason === sdk.CancellationReason.Error) {
          console.log(`CANCELED: ErrorCode=${cancellation.ErrorCode}`);
          console.log(`CANCELED: ErrorDetails=${cancellation.errorDetails}`);
        }
      }

      speechRecognizer.close();
    },
    (error: string) => {
      console.error(`An error occurred: ${error}`);
      speechRecognizer.close();
    },
  );
}

// Run the assessment.
// Make sure to install the SDK first: npm install microsoft-cognitiveservices-speech-sdk
// And replace placeholders and the audio file path.
// Set environment variables before running:
// export SPEECH_SUBSCRIPTION_KEY="YOUR_KEY"
// export SPEECH_SERVICE_REGION="YOUR_REGION"
performPronunciationAssessment();
