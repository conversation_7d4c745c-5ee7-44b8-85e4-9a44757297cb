# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

- To run the conversation transcriber: `npm run dev`
- To run the pronunciation assessment example: `npm run dev:assessment`
- To run the text-to-speech example: `npm run dev:tts`
- To build the project: `npm run build`

## Architecture

This is a Vite-based TypeScript project that uses the `microsoft-cognitiveservices-speech-sdk` to interact with Azure's Speech service.

The project is divided into three main examples:

- **Conversation Transcription (`src/main.ts`):** Transcribes speech from an audio file.
- **Pronunciation Assessment (`pronunciation-assessment-example.ts`):** Assesses the pronunciation of speech in an audio file against a reference text.
- **Text-to-Speech (`text-to-speech-example.ts`):** Converts text to speech and saves it as an audio file.

All scripts require the `SPEECH_KEY` and `SPEECH_REGION` environment variables to be set for authentication with the Azure Speech service.
