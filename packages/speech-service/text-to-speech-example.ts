import config from "./config.js";
import * as sdk from "microsoft-cognitiveservices-speech-sdk";
import readline from "readline";

console.log(config);

if (!config.SPEECH_KEY || !config.SPEECH_REGION) {
  console.error(
    "Please set the SPEECH_KEY and SPEECH_REGION environment variables.",
  );
  process.exit(1);
}

const audioFile: string = "cool.mp3";
// This example requires environment variables named "SPEECH_KEY" and "SPEECH_REGION"
const speechConfig: sdk.SpeechConfig = sdk.SpeechConfig.fromSubscription(
  config.SPEECH_KEY,
  config.SPEECH_REGION,
);
const audioConfig: sdk.AudioConfig =
  sdk.AudioConfig.fromAudioFileOutput(audioFile);

// The language of the voice that speaks.
speechConfig.speechSynthesisVoiceName = "en-US-TonyNeural";

// Create the speech synthesizer.
let synthesizer: sdk.SpeechSynthesizer | null = new sdk.SpeechSynthesizer(
  speechConfig,
  audioConfig,
);

const rl: readline.Interface = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

rl.question("Enter some text that you want to speak >\n> ", (text: string) => {
  rl.close();
  if (!synthesizer) {
    return;
  }
  // Start the synthesizer and wait for a result.
  synthesizer.speakTextAsync(
    text,
    (result: sdk.SpeechSynthesisResult) => {
      if (result.reason === sdk.ResultReason.SynthesizingAudioCompleted) {
        console.log("synthesis finished.");
      } else {
        console.error(
          "Speech synthesis canceled, " +
            result.errorDetails +
            "\nDid you set the speech resource key and region values?",
        );
      }
      synthesizer?.close();
      synthesizer = null;
    },
    (err: string) => {
      console.trace("err - " + err);
      synthesizer?.close();
      synthesizer = null;
    },
  );
  console.log("Now synthesizing to: " + audioFile);
});
