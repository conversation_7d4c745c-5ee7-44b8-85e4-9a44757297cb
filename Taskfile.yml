version: '3'

tasks:
  install:
    cmds:
      - pnpm install
    desc: "Installs dependencies."

  dev:
    cmds:
      - pnpm --filter speech-service-app dev
    desc: "Starts the development server."

  build:
    cmds:
      - pnpm --filter speech-service-app build
    desc: "Builds the application for production."

  start:
    cmds:
      - pnpm --filter speech-service-app start
    desc: "Starts the production server."

  test:
    cmds:
      - pnpm --filter speech-service-app test
    desc: "Runs the test suite."

  pronunciation-assessment:
    cmds:
      - pnpm exec vite-node packages/speech-service/pronunciation-assessment-example.ts
    desc: "Runs the pronunciation assessment example."
